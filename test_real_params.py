#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用真实浏览器参数的搜索功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_real_params():
    """测试使用真实浏览器参数的搜索API"""
    # 设置日志
    logger = setup_logger()
    logger.info("🔍 开始测试使用真实浏览器参数的搜索功能")
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化采集引擎
    collector = CollectorEngine(config_manager)
    
    try:
        # 首先打开登录浏览器
        logger.info("📱 正在打开登录浏览器...")
        if collector.open_login_browser():
            logger.info("✅ 登录浏览器打开成功，请确保已登录")
            
            # 等待用户确认登录
            input("请在浏览器中完成登录，然后按回车键继续...")
            
            # 检查浏览器实例
            if collector.page:
                logger.info("🔧 正在从浏览器获取真实API参数...")
                
                # 获取真实API参数
                real_params = collector.get_real_api_params_from_browser()
                logger.info(f"📋 获取到的真实参数: {real_params}")
                
                # 获取cookies信息
                collector.sync_cookies_to_session()
                token = collector.get_token_from_cookies()
                logger.info(f"🔑 获取到的token: {token[:20] if token else 'None'}...")
                
                # 测试关键词
                test_keyword = "手机"
                logger.info(f"🔍 开始测试搜索: {test_keyword}")
                
                # 定义进度回调
                def progress_callback(progress):
                    logger.info(f"📊 进度: 第{progress['current_page']}/{progress['total_pages']}页, "
                               f"当前页{progress['current_items']}条, 总计{progress['total_items']}条")
                
                # 采集数据
                results = collector.collect_search_data(
                    keyword=test_keyword,
                    max_pages=1,  # 只测试1页
                    min_want_count=1,
                    progress_callback=progress_callback
                )
                
                if results:
                    logger.info(f"✅ 测试成功！获取到 {len(results)} 条数据")
                    
                    # 显示前3条数据的详细信息
                    for i, item in enumerate(results[:3]):
                        logger.info(f"\n📱 商品 {i+1}:")
                        logger.info(f"  标题: {item.get('title', 'N/A')}")
                        logger.info(f"  价格: {item.get('price', 'N/A')}")
                        logger.info(f"  想要人数: {item.get('wantCount', 'N/A')}")
                        logger.info(f"  链接: {item.get('link', 'N/A')[:50]}...")
                        
                else:
                    logger.warning("❌ 测试失败：未获取到任何数据")
                    logger.info("💡 可能的原因:")
                    logger.info("  1. API参数不正确")
                    logger.info("  2. 签名算法有误")
                    logger.info("  3. cookies已过期")
                    logger.info("  4. 网络请求被拦截")
                    
            else:
                logger.error("❌ 浏览器实例不存在")
        else:
            logger.error("❌ 无法打开登录浏览器")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    finally:
        # 清理资源
        try:
            if collector.page:
                collector.page.quit()
                logger.info("🧹 浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    test_real_params()
