#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XY商品采集器 - Python版本
主程序入口文件
"""

import sys
import os
import logging
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QAction
from PyQt5.QtCore import QTimer, pyqtSignal, QObject
from PyQt5.QtGui import QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.main_window import MainWindow
from core.auth_manager import AuthManager
from core.config_manager import ConfigManager
from utils.logger import setup_logger

class XYCollectorApp(QObject):
    """XY采集器主应用程序类"""
    
    def __init__(self):
        super().__init__()
        self.app = None
        self.main_window = None
        self.tray_icon = None
        self.auth_manager = AuthManager()
        self.config_manager = ConfigManager()
        
        # 设置日志
        self.logger = setup_logger()
        self.logger.info("XY采集器启动中...")
        
    def init_app(self):
        """初始化应用程序"""
        self.app = QApplication(sys.argv)
        self.app.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出程序
        
        # 设置应用程序信息
        self.app.setApplicationName("XY商品采集器")
        self.app.setApplicationVersion("3.0")
        self.app.setOrganizationName("XY采集器")
        
        # 检查是否支持系统托盘
        if not QSystemTrayIcon.isSystemTrayAvailable():
            self.logger.error("系统不支持系统托盘")
            return False
            
        return True
        
    def create_tray_icon(self):
        """创建系统托盘图标"""
        self.tray_icon = QSystemTrayIcon(self)
        
        # 设置托盘图标
        icon_path = project_root / "resources" / "icon.png"
        if icon_path.exists():
            self.tray_icon.setIcon(QIcon(str(icon_path)))
        else:
            # 使用默认图标
            self.tray_icon.setIcon(self.app.style().standardIcon(self.app.style().SP_ComputerIcon))
            
        # 创建托盘菜单
        tray_menu = QMenu()
        
        # 显示主窗口
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_main_window)
        tray_menu.addAction(show_action)
        
        tray_menu.addSeparator()
        
        # 退出程序
        quit_action = QAction("退出程序", self)
        quit_action.triggered.connect(self.quit_app)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.setToolTip("XY商品采集器 v3.0")
        
        # 双击托盘图标显示主窗口
        self.tray_icon.activated.connect(self.on_tray_activated)
        
        self.tray_icon.show()
        
    def on_tray_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_main_window()
            
    def show_main_window(self):
        """显示主窗口"""
        if self.main_window is None:
            self.main_window = MainWindow(self.auth_manager, self.config_manager)
            
        self.main_window.show()
        self.main_window.raise_()
        self.main_window.activateWindow()
        
    def quit_app(self):
        """退出应用程序"""
        self.logger.info("用户退出程序")
        if self.main_window:
            self.main_window.close()
        self.app.quit()
        
    def run(self):
        """运行应用程序"""
        if not self.init_app():
            return 1
            
        # 检查授权状态
        if not self.auth_manager.check_auth():
            self.logger.info("需要进行授权验证")
            
        # 创建系统托盘
        self.create_tray_icon()
        
        # 显示主窗口
        self.show_main_window()
        
        self.logger.info("XY采集器启动完成")
        
        # 运行应用程序
        return self.app.exec_()

def main():
    """主函数"""
    try:
        app = XYCollectorApp()
        return app.run()
    except Exception as e:
        logging.error(f"程序启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
