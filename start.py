#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XY商品采集器启动脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    try:
        print("🚀 启动XY商品采集器...")
        
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from ui.main_window import MainWindow
        from core.auth_manager import AuthManager
        from core.config_manager import ConfigManager
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("XY商品采集器")
        app.setApplicationVersion("4.0")
        
        # 创建管理器
        auth_manager = AuthManager()
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = MainWindow(auth_manager, config_manager)
        main_window.show()
        
        # 显示启动信息
        QMessageBox.information(
            main_window, 
            "XY商品采集器 v4.0", 
            f"🎉 欢迎使用XY商品采集器！\n\n"
            f"✨ 功能特性：\n"
            f"• 关键词搜索采集\n"
            f"• 店铺商品采集\n"
            f"• 实时数据展示\n"
            f"• 多格式数据导出\n\n"
            f"🔧 当前模式：调试模式\n"
            f"• 使用模拟数据进行演示\n"
            f"• 授权验证已跳过\n\n"
            f"📝 使用方法：\n"
            f"1. 输入搜索关键词（如：手机）\n"
            f"2. 设置采集参数\n"
            f"3. 点击开始搜索\n"
            f"4. 查看采集结果\n\n"
            f"💡 关闭此对话框开始使用"
        )
        
        print("✅ 应用程序启动成功")
        
        # 运行应用程序
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请安装必要的依赖包:")
        print("pip install PyQt5 requests openpyxl")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
