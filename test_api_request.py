#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API请求功能
"""

import sys
import os
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_api_request():
    """测试API请求"""
    logger = setup_logger()
    logger.info("🔍 开始测试API请求")
    
    try:
        # 初始化
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 构建搜索参数
        params = collector._build_search_params("手机", 1)
        logger.info(f"📋 请求参数: {params}")
        
        # 发送请求
        api_url = collector.api_config['search_api']
        logger.info(f"🌐 请求URL: {api_url}")
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.goofish.com/',
            'Origin': 'https://www.goofish.com'
        }
        
        logger.info("📡 发送API请求...")
        response = requests.get(api_url, params=params, headers=headers, timeout=15)
        
        logger.info(f"📊 响应状态码: {response.status_code}")
        logger.info(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"✅ 成功获取响应数据")
                logger.info(f"📋 响应结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                
                # 检查响应内容
                if isinstance(data, dict):
                    if 'ret' in data:
                        logger.info(f"🔍 返回码: {data.get('ret')}")
                    if 'data' in data:
                        logger.info(f"🔍 数据字段: {type(data.get('data'))}")
                        if isinstance(data.get('data'), dict):
                            logger.info(f"🔍 数据子字段: {list(data['data'].keys())}")
                    if 'message' in data:
                        logger.info(f"🔍 消息: {data.get('message')}")
                
                # 显示部分响应内容
                response_str = json.dumps(data, ensure_ascii=False, indent=2)
                if len(response_str) > 1000:
                    logger.info(f"📄 响应内容(前1000字符): {response_str[:1000]}...")
                else:
                    logger.info(f"📄 完整响应内容: {response_str}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON解析失败: {e}")
                logger.info(f"📄 原始响应内容: {response.text[:500]}...")
        else:
            logger.error(f"❌ 请求失败，状态码: {response.status_code}")
            logger.info(f"📄 错误响应: {response.text[:500]}...")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_api_request()
