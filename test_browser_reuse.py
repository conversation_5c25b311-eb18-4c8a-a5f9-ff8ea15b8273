#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器复用逻辑
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_browser_reuse():
    """测试浏览器复用逻辑"""
    # 设置日志
    logger = setup_logger()
    logger.info("🧪 开始测试浏览器复用逻辑")
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化采集引擎
    collector = CollectorEngine(config_manager)
    
    try:
        # 1. 首先打开登录浏览器
        logger.info("📱 步骤1: 打开登录浏览器")
        if collector.open_login_browser():
            logger.info("✅ 登录浏览器已打开")
            
            # 等待用户确认登录
            input("请在浏览器中完成登录，然后按回车键继续...")
            
            # 2. 检查浏览器状态
            logger.info("📱 步骤2: 检查浏览器状态")
            if collector.page:
                logger.info("✅ 浏览器实例存在")
                
                # 获取当前cookies
                cookies = collector.page.cookies()
                logger.info(f"📊 当前cookies数量: {len(cookies)}")
                
                # 检查核心cookies
                core_cookies = ['_m_h5_tk', '_tb_token_', 't']
                for cookie_name in core_cookies:
                    if cookie_name in cookies:
                        logger.info(f"✅ 核心Cookie {cookie_name}: 存在")
                    else:
                        logger.warning(f"❌ 核心Cookie {cookie_name}: 缺失")
                
                # 3. 测试切换到后台模式
                logger.info("📱 步骤3: 切换浏览器到后台模式")
                collector.switch_to_headless_mode()
                
                # 4. 测试API采集
                logger.info("📱 步骤4: 测试API采集")
                test_keyword = "手机"
                
                def progress_callback(progress):
                    logger.info(f"📊 进度: 第{progress['current_page']}/{progress['total_pages']}页")
                
                results = collector.collect_search_data(
                    keyword=test_keyword,
                    max_pages=1,  # 只测试1页
                    min_want_count=1,
                    progress_callback=progress_callback
                )
                
                if results:
                    logger.info(f"✅ 采集成功！获取到 {len(results)} 条数据")
                    
                    # 显示第一条数据
                    if results:
                        item = results[0]
                        logger.info(f"📱 示例商品:")
                        logger.info(f"  标题: {item.get('title', 'N/A')}")
                        logger.info(f"  价格: {item.get('price', 'N/A')}")
                        logger.info(f"  想要人数: {item.get('wantCount', 'N/A')}")
                else:
                    logger.warning("❌ 采集失败：未获取到任何数据")
                    
            else:
                logger.error("❌ 浏览器实例不存在")
        else:
            logger.error("❌ 无法打开登录浏览器")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        
    finally:
        # 询问是否关闭浏览器
        choice = input("测试完成，是否关闭浏览器？(y/n): ")
        if choice.lower() == 'y':
            try:
                collector.close()
            except:
                pass
            logger.info("🔚 浏览器已关闭")
        else:
            logger.info("🔚 浏览器保持开启状态")

if __name__ == "__main__":
    test_browser_reuse()
