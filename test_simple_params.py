#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试真实参数获取功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def main():
    """简单测试"""
    logger = setup_logger()
    logger.info("🔍 开始简单测试")
    
    try:
        # 初始化
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        logger.info("✅ 初始化成功")
        
        # 测试构建搜索参数（不需要浏览器）
        params = collector._build_search_params("手机", 1)
        logger.info(f"📋 构建的搜索参数: {params}")
        
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
