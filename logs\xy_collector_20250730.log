2025-07-30 14:34:29 - XYCollector - INFO - XY采集器启动中...
2025-07-30 14:34:29 - XYCollector - INFO - 需要进行授权验证
2025-07-30 14:34:30 - XYCollector - INFO - XY采集器启动完成
2025-07-30 14:43:08 - XYCollector - INFO - Logger test successful
2025-07-30 14:43:57 - XYCollector - INFO - 日志模块测试成功
2025-07-30 14:44:58 - XYCollector - INFO - 日志系统测试成功
2025-07-30 14:48:44 - XYCollector - INFO - XY采集器启动中...
2025-07-30 14:48:44 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:45:06 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:45:07 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:47:32 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:47:32 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:48:58 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:48:58 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:56:29 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:56:30 - XYCollector - INFO - XY采集器启动完成
2025-07-30 15:57:35 - XYCollector - INFO - XY采集器启动中...
2025-07-30 15:57:36 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:05:07 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:05:07 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:06:05 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:06:05 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:12:58 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:12:59 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:18:25 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:18:26 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:19:05 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:19:05 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:25:42 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:25:42 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:27:39 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:27:40 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:28:18 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:28:19 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:31:31 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:31:31 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:32:51 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:32:51 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:33:29 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:33:30 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:38:14 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:38:15 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:38:49 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:38:49 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:44:43 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:44:43 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:49:36 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:49:36 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:52:12 - XYCollector - INFO - 🧪 开始测试搜索API修复效果
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 检测到有效的登录cookies
2025-07-30 16:52:12 - XYCollector - INFO - 📊 Cookies总数: 12
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 核心Cookie _m_h5_tk: 存在
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 核心Cookie _tb_token_: 存在
2025-07-30 16:52:12 - XYCollector - INFO - ✅ 核心Cookie t: 存在
2025-07-30 16:52:12 - XYCollector - INFO - 🔍 测试关键词: 手机
2025-07-30 16:58:03 - XYCollector - INFO - XY采集器启动中...
2025-07-30 16:58:03 - XYCollector - INFO - XY采集器启动完成
2025-07-30 16:58:54 - XYCollector - WARNING - ❌ 测试失败：未获取到任何数据
2025-07-30 16:58:54 - XYCollector - INFO - 💡 可能的原因：
2025-07-30 16:58:54 - XYCollector - INFO -    1. 服务器过载，请稍后重试
2025-07-30 16:58:54 - XYCollector - INFO -    2. 登录状态已过期，请重新登录
2025-07-30 16:58:54 - XYCollector - INFO -    3. API参数需要调整
2025-07-30 16:58:54 - XYCollector - INFO - 🔚 测试完成
2025-07-30 17:47:14 - XYCollector - INFO - 🔍 开始测试使用真实浏览器参数的搜索功能
2025-07-30 17:47:14 - XYCollector - INFO - 📱 正在打开登录浏览器...
2025-07-30 17:47:14 - XYCollector - ERROR - ❌ 测试过程中出现错误: 'CollectorEngine' object has no attribute 'open_login_browser'
2025-07-30 17:47:14 - XYCollector - ERROR - 详细错误信息: Traceback (most recent call last):
  File "E:\Cursor源码\XY商品采集插件\test_real_params.py", line 34, in test_real_params
    if collector.open_login_browser():
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'CollectorEngine' object has no attribute 'open_login_browser'

2025-07-30 17:47:58 - XYCollector - INFO - 🔍 开始简单测试
2025-07-30 17:47:58 - XYCollector - INFO - ✅ 初始化成功
2025-07-30 17:47:58 - XYCollector - INFO - 📋 构建的搜索参数: {'jsv': '2.7.2', 'appKey': '********', 't': '*************', 'sign': '2f0c24cf832e1606c278b291803742be', 'v': '1.0', 'type': 'originaljson', 'accountSite': 'xianyu', 'dataType': 'json', 'timeout': '20000', 'api': 'mtop.taobao.idlehome.home.webpc.feed', 'sessionOption': 'AutoLoginOnly', 'spm_cnt': 'a21ybx.home.0.0', 'data': '{"keyword":"手机","page":1,"pageSize":20,"sortType":"default"}'}
2025-07-30 17:47:58 - XYCollector - INFO - ✅ 测试完成
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 开始测试API请求
2025-07-30 17:48:35 - XYCollector - INFO - 📋 请求参数: {'jsv': '2.7.2', 'appKey': '********', 't': '*************', 'sign': 'e37c8f1105b94fe94918ace408b1fda4', 'v': '1.0', 'type': 'originaljson', 'accountSite': 'xianyu', 'dataType': 'json', 'timeout': '20000', 'api': 'mtop.taobao.idlehome.home.webpc.feed', 'sessionOption': 'AutoLoginOnly', 'spm_cnt': 'a21ybx.home.0.0', 'data': '{"keyword":"手机","page":1,"pageSize":20,"sortType":"default"}'}
2025-07-30 17:48:35 - XYCollector - INFO - 🌐 请求URL: https://h5api.m.goofish.com/h5/mtop.taobao.idlehome.home.webpc.feed/1.0/
2025-07-30 17:48:35 - XYCollector - INFO - 📡 发送API请求...
2025-07-30 17:48:35 - XYCollector - INFO - 📊 响应状态码: 200
2025-07-30 17:48:35 - XYCollector - INFO - 📊 响应头: {'Date': 'Wed, 30 Jul 2025 09:48:37 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Content-Length': '111', 'Connection': 'keep-alive', 'Vary': 'Accept-Encoding', 'Set-Cookie': 'cookie2=1718220ae3f4d4d745665a4ba691f105;Path=/;Domain=.goofish.com;Max-Age=-1;HttpOnly, mtop_partitioned_detect=1;Path=/;Domain=.goofish.com;Max-Age=5400;SameSite=None;Secure;Partitioned, _m_h5_tk=af0ae1a8944ce3e36b9dbd67531d6a04_1753878277186;Path=/;Domain=.goofish.com;Max-Age=5400;SameSite=None;Secure, _m_h5_tk_enc=d2a41d3c11e9865beaf79138c7f5de75;Path=/;Domain=.goofish.com;Max-Age=5400;SameSite=None;Secure', 's_tid': '2150451d17538689171818524e1aff', 'x-node': 'dafe669a6659e576db3ae0189a6a53f3', 'Access-Control-Allow-Origin': 'https://www.goofish.com', 's_ip': '457079564a4a776e793147593262773d', 'x-eagleeye-id': '2150451d17538689171818524e1aff', 's_v': '*******', 'Pragma': 'no-cache', 'P3P': "CP='CURa ADMa DEVa PSAo PSDo OUR BUS UNI PUR INT DEM STA PRE COM NAV OTC NOI DSP COR'", 's_tag': '285873024335892|134217728^|^^', 'Access-Control-Expose-Headers': 'x-eagleeye-id', 'Cache-Control': 'no-cache', 'Access-Control-Allow-Credentials': 'true', 's_group': 'tao-session', 's_status': 'STATUS_NOT_EXISTED', 's_ucode': 'CN:CENTER', 'X-Powered-By': 'm.taobao.com', 'Ups-Target-Key': 'mtop.uncenter.idle-home', 'X-protocol': 'HTTP/1.1', 'EagleEye-TraceId': '2150451d17538689171818524e1aff', 'Strict-Transport-Security': 'max-age=0', 's-brt': '4', 's-rt': '8', 's-cunit': '0'}
2025-07-30 17:48:35 - XYCollector - INFO - ✅ 成功获取响应数据
2025-07-30 17:48:35 - XYCollector - INFO - 📋 响应结构: ['api', 'data', 'ret', 'v']
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 返回码: ['FAIL_SYS_TOKEN_EMPTY::令牌为空']
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 数据字段: <class 'dict'>
2025-07-30 17:48:35 - XYCollector - INFO - 🔍 数据子字段: []
2025-07-30 17:48:35 - XYCollector - INFO - 📄 完整响应内容: {
  "api": "mtop.taobao.idlehome.home.webpc.feed",
  "data": {},
  "ret": [
    "FAIL_SYS_TOKEN_EMPTY::令牌为空"
  ],
  "v": "1.0"
}
